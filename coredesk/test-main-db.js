/**
 * Test script to verify database functionality in main process
 */

const { app } = require('electron');
const path = require('path');
const logger = require('./src/js/utils/MainLogger');
const DatabaseService = require('./src/js/services/DatabaseService');

async function testDatabase() {
    try {
        console.log('🔍 Testing database in main process...');
        
        // Initialize database
        const database = new DatabaseService(logger);
        const initialized = await database.initialize();
        
        console.log('✅ Database initialized:', initialized);
        
        if (initialized) {
            // Test connection
            const connection = database.getConnection();
            console.log('✅ Connection obtained:', !!connection);
            console.log('✅ Connection type:', typeof connection);
            console.log('✅ Has all method:', typeof connection.all === 'function');
            
            // Test query
            const result = await database.execute('SELECT name FROM sqlite_master WHERE type="table" AND name="installed_modules"', []);
            console.log('✅ Query result:', result);
            
            // Test global assignment
            global.database = connection;
            console.log('✅ Global database assigned:', !!global.database);
            console.log('✅ Global database type:', typeof global.database);
            
            // Test direct query on global
            if (global.database) {
                global.database.all('SELECT name FROM sqlite_master WHERE type="table"', [], (err, rows) => {
                    if (err) {
                        console.log('❌ Direct global query error:', err);
                    } else {
                        console.log('✅ Direct global query success:', rows);
                    }
                    
                    // Close and exit
                    database.close();
                    process.exit(0);
                });
            } else {
                console.log('❌ Global database not available');
                process.exit(1);
            }
        } else {
            console.log('❌ Database initialization failed');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

// Wait for app to be ready
app.whenReady().then(() => {
    testDatabase();
});

app.on('window-all-closed', () => {
    app.quit();
});
