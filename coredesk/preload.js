/**
 * preload.js
 * Secure bridge between main process and renderer process
 * Version compatible with CoreDesk Framework
 */

const { contextBridge, ipcRenderer } = require('electron');

// Simple ApiExposer fallback to avoid module not found errors
class ApiExposer {
    constructor() {
        this.validChannels = [
            'app:initialized',
            'app:initialization-error',
            'license:activated',
            'sync:completed',
            'sync:error',
            'window:maximized',
            'update-available',
            'update-progress',
            'update-downloaded',
            'update-error',
            'database:ready',
            'persistence:ready'
        ];
    }

    getElectronAPI() {
        return {
            // Window controls
            minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
            maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
            toggleMaximizeWindow: () => ipcRenderer.invoke('window:maximize'),
            closeWindow: () => ipcRenderer.invoke('window:close'),
            window: {
                minimize: () => ipcRenderer.invoke('window:minimize'),
                maximize: () => ipcRenderer.invoke('window:maximize'),
                toggleMaximize: () => ipcRenderer.invoke('window:maximize'),
                close: () => ipcRenderer.invoke('window:close'),
                move: (deltaX, deltaY) => ipcRenderer.invoke('window:move', deltaX, deltaY),
            },
            
            // App controls
            app: {
                getVersion: () => ipcRenderer.invoke('app-get-version'),
                quit: () => ipcRenderer.invoke('app-quit'),
            },
            
            // Database
            database: {
                execute: (query, params) => ipcRenderer.invoke('db:execute', query, params),
                query: (query, params) => ipcRenderer.invoke('db:query', query, params),
                getSchema: () => ipcRenderer.invoke('db:getSchema'),
            },
            
            // JSON Persistence
            jsonPersistence: {
                registerModule: (moduleData) => ipcRenderer.invoke('json-persistence:register-module', moduleData),
                getInstalledModules: () => ipcRenderer.invoke('json-persistence:get-installed-modules'),
                getModule: (moduleId) => ipcRenderer.invoke('json-persistence:get-module', moduleId),
                unregisterModule: (moduleId) => ipcRenderer.invoke('json-persistence:unregister-module', moduleId),
                getStats: () => ipcRenderer.invoke('json-persistence:get-stats')
            },
            
            // System
            system: {
                getInfo: () => ipcRenderer.invoke('system-get-info'),
                getUsername: () => ipcRenderer.invoke('system:getUsername'),
            },
            
            // File System
            fileSystem: {
                writeFile: (filePath, data, options) => ipcRenderer.invoke('fs:writeFile', filePath, data, options),
                readFile: (filePath, options) => ipcRenderer.invoke('fs:readFile', filePath, options),
                deleteFile: (filePath) => ipcRenderer.invoke('fs:deleteFile', filePath),
                exists: (filePath) => ipcRenderer.invoke('fs:exists', filePath),
                createDirectory: (dirPath) => ipcRenderer.invoke('fs:createDirectory', dirPath),
                listDirectory: (dirPath) => ipcRenderer.invoke('fs:listDirectory', dirPath),
                readDirectory: (dirPath) => ipcRenderer.invoke('fs:listDirectory', dirPath), // Alias for compatibility
                getFileStats: (filePath) => ipcRenderer.invoke('fs:getFileStats', filePath),
                getHomeDirectory: () => ipcRenderer.invoke('fs:getHomeDirectory'),
                getCoreDeskPath: () => ipcRenderer.invoke('fs:getCoreDeskPath'),
                getModulesPath: () => ipcRenderer.invoke('fs:getModulesPath'),
                setCoreDeskPath: (newPath) => ipcRenderer.invoke('fs:setCoreDeskPath', newPath),
                navigateToParent: (currentPath) => ipcRenderer.invoke('fs:navigateToParent', currentPath)
            },

            // Updates
            updateCheck: () => ipcRenderer.invoke('update:check'),
            updateDownload: () => ipcRenderer.invoke('update:download'),
            updateInstall: () => ipcRenderer.invoke('update:install'),
            updateGetStatus: () => ipcRenderer.invoke('update:get-status'),
            updateSetChannel: (channel) => ipcRenderer.invoke('update:set-channel', channel),
            
            // Update event listeners
            onUpdateAvailable: (callback) => ipcRenderer.on('update-available', callback),
            onUpdateProgress: (callback) => ipcRenderer.on('update-progress', callback),
            onUpdateDownloaded: (callback) => ipcRenderer.on('update-downloaded', callback),
            onUpdateError: (callback) => ipcRenderer.on('update-error', callback),
            
            // System info shortcut for backwards compatibility
            getSystemInfo: () => ipcRenderer.invoke('system-get-info'),
            
            // Events
            on: (channel, callback) => {
                if (this.validChannels.includes(channel)) {
                    ipcRenderer.on(channel, callback);
                }
            },
            
            removeAllListeners: (channel) => {
                ipcRenderer.removeAllListeners(channel);
            }
        };
    }
}

// Preload bridge class
class CoreDeskPreloadBridge {
    constructor() {
        this.apiExposer = new ApiExposer();
        this.initialize();
    }

    initialize() {
        try {
            console.log('[Preload] Initializing CoreDesk preload bridge...');
            
            // Expose safe APIs to renderer
            this.exposeAPIs();
            
            console.log('[Preload] CoreDesk preload bridge initialized successfully');
            
        } catch (error) {
            console.error('[Preload] Error initializing preload bridge:', error);
        }
    }

    exposeAPIs() {
        // Get APIs from ApiExposer
        const electronAPI = this.apiExposer.getElectronAPI();
        
        // Expose the main electronAPI
        contextBridge.exposeInMainWorld('electronAPI', electronAPI);

        // Expose utilities and additional APIs
        contextBridge.exposeInMainWorld('coreDesk', {
            version: '2.0.0',
            platform: process.platform,
            environment: process.env.NODE_ENV || 'development',
            
            // Additional legacy support
            window: electronAPI.window,
            app: electronAPI.app,
            database: electronAPI.database,
            jsonPersistence: electronAPI.jsonPersistence,
            system: electronAPI.system,
            fileSystem: electronAPI.fileSystem,
            getSystemInfo: electronAPI.getSystemInfo,
            on: electronAPI.on,
            removeAllListeners: electronAPI.removeAllListeners
        });
        
        console.log('[Preload] APIs exposed successfully');
    }
}

// Initialize the preload bridge
new CoreDeskPreloadBridge();