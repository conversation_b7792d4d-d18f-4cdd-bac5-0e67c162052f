/**
 * DatabaseHandlers.js
 * Database operation IPC handlers for main process
 */

const { ipcMain } = require('electron');

class DatabaseHandlers {
    constructor(logger) {
        this.logger = logger;
        this.setupHandlers();
    }

    setupHandlers() {
        ipcMain.handle('db:query', async (event, sql, params = []) => {
            return this.executeQuery(sql, params);
        });

        ipcMain.handle('db:execute', async (event, sql, params = []) => {
            this.logger?.info('DatabaseHandlers', 'db:execute handler called', {
                sql: sql ? sql.substring(0, 50) + '...' : 'null',
                params: params,
                globalDatabaseExists: !!global.database,
                globalDatabaseType: typeof global.database
            });
            return this.executeQuery(sql, params);
        });

        ipcMain.handle('db:getSchema', async () => {
            return this.getSchema();
        });
    }

    async executeQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.logger?.info('DatabaseHandlers', 'executeQuery called', {
                sql: sql.substring(0, 100) + '...',
                params: params,
                globalDatabaseExists: !!global.database,
                globalDatabaseType: typeof global.database
            });

            if (!global.database) {
                const error = new Error('Database not initialized');
                this.logger?.error('DatabaseHandlers', 'Database not available', error);
                resolve({
                    success: false,
                    error: 'Database not initialized',
                    data: []
                });
                return;
            }

            try {
                // Validate SQL input
                if (!sql || typeof sql !== 'string') {
                    throw new Error('Invalid SQL query');
                }

                // Sanitize and determine query type
                const sqlLower = sql.toLowerCase().trim();
                
                if (sqlLower.startsWith('select')) {
                    this.executeSelectQuery(sql, params, resolve, reject);
                } else if (this.isModificationQuery(sqlLower)) {
                    this.executeModificationQuery(sql, params, resolve, reject);
                } else if (this.isDDLQuery(sqlLower)) {
                    this.executeDDLQuery(sql, resolve, reject);
                } else {
                    throw new Error('Unsupported query type');
                }
            } catch (error) {
                this.logger?.error('DatabaseHandlers', 'Query execution failed', error);
                reject(error);
            }
        });
    }

    executeSelectQuery(sql, params, resolve, reject) {
        global.database.all(sql, params, (err, rows) => {
            if (err) {
                this.logger?.error('DatabaseHandlers', 'SELECT query failed', err);
                resolve({
                    success: false,
                    error: err.message,
                    data: []
                });
            } else {
                this.logger?.info('DatabaseHandlers', 'SELECT query successful', {
                    rowCount: rows ? rows.length : 0,
                    firstRow: rows && rows.length > 0 ? rows[0] : null
                });
                resolve({
                    success: true,
                    data: rows || [],
                    rowCount: rows ? rows.length : 0
                });
            }
        });
    }

    executeModificationQuery(sql, params, resolve, reject) {
        global.database.run(sql, params, function(err) {
            if (err) {
                this.logger?.error('DatabaseHandlers', 'Modification query failed', err);
                resolve({
                    success: false,
                    error: err.message,
                    data: []
                });
            } else {
                this.logger?.info('DatabaseHandlers', 'Modification query successful', {
                    changes: this.changes || 0,
                    lastID: this.lastID
                });
                resolve({
                    success: true,
                    data: [],
                    rowCount: this.changes || 0,
                    lastID: this.lastID
                });
            }
        });
    }

    executeDDLQuery(sql, resolve, reject) {
        global.database.exec(sql, (err) => {
            if (err) {
                this.logger?.error('DatabaseHandlers', 'DDL query failed', err);
                reject(err);
            } else {
                resolve({ success: true });
            }
        });
    }

    isModificationQuery(sql) {
        return sql.startsWith('insert') || 
               sql.startsWith('update') || 
               sql.startsWith('delete');
    }

    isDDLQuery(sql) {
        return sql.startsWith('create') || 
               sql.startsWith('drop') || 
               sql.startsWith('alter') ||
               sql.startsWith('pragma');
    }

    async getSchema() {
        try {
            const sql = "SELECT name, sql FROM sqlite_master WHERE type='table'";
            return await this.executeQuery(sql, []);
        } catch (error) {
            this.logger?.error('DatabaseHandlers', 'Failed to get schema', error);
            throw error;
        }
    }

    cleanup() {
        ipcMain.removeAllListeners('db:query');
        ipcMain.removeAllListeners('db:execute');
        ipcMain.removeAllListeners('db:getSchema');
    }
}

module.exports = DatabaseHandlers;